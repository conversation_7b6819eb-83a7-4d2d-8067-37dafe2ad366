import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import TOUCH

# LAB阈值测试程序
class LABThresholdTester:
    def __init__(self):
        # LAB阈值参数
        self.lab_params = {
            'L_MIN': 0, 'L_MAX': 50,      # L通道 (亮度)
            'A_MIN': -20, 'A_MAX': 20,    # A通道 (绿-红)
            'B_MIN': -20, 'B_MAX': 20     # B通道 (蓝-黄)
        }
        
        # 当前调整的参数
        self.current_param = 0
        self.param_names = ["L_MIN", "L_MAX", "A_MIN", "A_MAX", "B_MIN", "B_MAX"]
        
        # 调整步长
        self.adjust_steps = {
            'L_MIN': 1, 'L_MAX': 1,
            'A_MIN': 2, 'A_MAX': 2,
            'B_MIN': 2, 'B_MAX': 2
        }
        
        # 触摸相关
        self.last_touch_time = 0
        self.touch_debounce = 200  # 200ms防抖
        
    def get_lab_threshold(self):
        """获取当前LAB阈值"""
        return [(
            self.lab_params['L_MIN'], self.lab_params['L_MAX'],
            self.lab_params['A_MIN'], self.lab_params['A_MAX'],
            self.lab_params['B_MIN'], self.lab_params['B_MAX']
        )]
    
    def which_button(self, x, y):
        """判断按下的是哪个按钮"""
        # 参数选择按钮（左侧）
        if x < 150 and y >= 50:
            param_index = (y - 50) // 40
            if param_index < len(self.param_names) and (y - 50) % 40 < 35:
                return f"param_{param_index}"
        
        # 减少按钮
        if 650 <= x <= 750 and 150 <= y <= 200:
            return "minus"
        
        # 增加按钮
        if 650 <= x <= 750 and 220 <= y <= 270:
            return "plus"
        
        # 重置按钮
        if 650 <= x <= 750 and 300 <= y <= 350:
            return "reset"
            
        return None
    
    def handle_touch(self, touch_x, touch_y):
        """处理触摸事件"""
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, self.last_touch_time) < self.touch_debounce:
            return False
        
        button = self.which_button(touch_x, touch_y)
        if not button:
            return False
        
        self.last_touch_time = current_time
        
        if button.startswith("param_"):
            param_index = int(button.split("_")[1])
            self.current_param = param_index
            return True
        
        elif button == "minus":
            param_name = self.param_names[self.current_param]
            step = self.adjust_steps[param_name]
            min_val = self.get_param_min_value(param_name)
            self.lab_params[param_name] = max(min_val, self.lab_params[param_name] - step)
            return True
        
        elif button == "plus":
            param_name = self.param_names[self.current_param]
            step = self.adjust_steps[param_name]
            max_val = self.get_param_max_value(param_name)
            self.lab_params[param_name] = min(max_val, self.lab_params[param_name] + step)
            return True
        
        elif button == "reset":
            # 重置为默认值
            self.lab_params = {
                'L_MIN': 0, 'L_MAX': 50,
                'A_MIN': -20, 'A_MAX': 20,
                'B_MIN': -20, 'B_MAX': 20
            }
            print("阈值已重置")
            return True
        
        return False
    
    def get_param_min_value(self, param_name):
        min_values = {
            'L_MIN': 0, 'L_MAX': 0,
            'A_MIN': -128, 'A_MAX': -128,
            'B_MIN': -128, 'B_MAX': -128
        }
        return min_values.get(param_name, 0)
    
    def get_param_max_value(self, param_name):
        max_values = {
            'L_MIN': 100, 'L_MAX': 100,
            'A_MIN': 127, 'A_MAX': 127,
            'B_MIN': 127, 'B_MAX': 127
        }
        return max_values.get(param_name, 100)
    
    def draw_interface(self, img):
        """绘制调试界面"""
        # 背景
        img.draw_rectangle(0, 0, 800, 480, color=(40, 40, 40), thickness=2, fill=True)
        
        # 标题
        img.draw_string_advanced(10, 10, 25, "LAB Threshold Tester", color=(255, 255, 255))
        
        # 参数选择按钮（左侧）
        for i, param_name in enumerate(self.param_names):
            y_pos = 50 + i * 40
            color = (100, 255, 100) if i == self.current_param else (150, 150, 150)
            img.draw_rectangle(10, y_pos, 140, 35, color=color, thickness=1, fill=True)
            img.draw_string_advanced(15, y_pos + 8, 18, param_name, color=(0, 0, 0))
            
            # 显示当前值
            value = self.lab_params[param_name]
            img.draw_string_advanced(90, y_pos + 8, 16, f"{value}", color=(0, 0, 0))
        
        # 控制按钮（右侧）
        # 减少按钮
        img.draw_rectangle(650, 150, 100, 50, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(690, 165, 25, "-", color=(0, 0, 0))
        
        # 增加按钮
        img.draw_rectangle(650, 220, 100, 50, color=(100, 255, 100), thickness=2, fill=True)
        img.draw_string_advanced(690, 235, 25, "+", color=(0, 0, 0))
        
        # 重置按钮
        img.draw_rectangle(650, 300, 100, 50, color=(255, 255, 100), thickness=2, fill=True)
        img.draw_string_advanced(665, 315, 18, "RESET", color=(0, 0, 0))
        
        # 显示当前选中的参数
        current_param = self.param_names[self.current_param]
        current_value = self.lab_params[current_param]
        img.draw_rectangle(200, 400, 300, 60, color=(200, 200, 200), thickness=2, fill=True)
        img.draw_string_advanced(210, 415, 20, f"Current: {current_param}", color=(0, 0, 0))
        img.draw_string_advanced(210, 440, 20, f"Value: {current_value}", color=(0, 0, 0))
        
        # 显示当前LAB阈值
        img.draw_string_advanced(200, 300, 16, f"LAB Threshold:", color=(255, 255, 255))
        img.draw_string_advanced(200, 320, 16, f"L: {self.lab_params['L_MIN']}-{self.lab_params['L_MAX']}", color=(255, 255, 255))
        img.draw_string_advanced(200, 340, 16, f"A: {self.lab_params['A_MIN']}-{self.lab_params['A_MAX']}", color=(255, 255, 255))
        img.draw_string_advanced(200, 360, 16, f"B: {self.lab_params['B_MIN']}-{self.lab_params['B_MAX']}", color=(255, 255, 255))

# 主程序
sensor = None
touch = None

try:
    # 初始化测试器
    tester = LABThresholdTester()
    
    # 摄像头初始化
    sensor = Sensor(width=800, height=480)
    sensor.reset()
    # 摄像头倒着装，需要镜像和翻转
    sensor.set_hmirror(True)
    sensor.set_vflip(True)
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)
    
    # 显示初始化
    Display.init(Display.ST7701, to_ide=True, width=800, height=480)
    MediaManager.init()
    sensor.run()
    
    # 触摸屏初始化
    touch = TOUCH(0)
    
    clock = time.clock()
    print("LAB阈值测试器启动")
    
    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
        
        # 处理触摸
        touch_points = touch.read()
        if touch_points:
            for point in touch_points:
                tester.handle_touch(point.x, point.y)
        
        # 绘制界面
        tester.draw_interface(img)
        
        # 在预览区域显示LAB检测结果
        try:
            # 获取当前LAB阈值
            lab_threshold = tester.get_lab_threshold()
            
            # 进行LAB检测
            blobs = img.find_blobs(lab_threshold, merge=True, area_threshold=1000)
            
            # 在原图上绘制检测结果
            blob_count = 0
            for blob in blobs:
                img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=10, thickness=2)
                blob_count += 1
            
            # 显示检测到的blob数量
            img.draw_string_advanced(200, 50, 20, f"Blobs found: {blob_count}", color=(0, 255, 0))
            
        except Exception as e:
            img.draw_string_advanced(200, 50, 18, f"Detection error: {str(e)[:30]}", color=(255, 0, 0))
        
        # 显示FPS
        img.draw_string_advanced(10, 450, 16, f"FPS: {clock.fps():.1f}", color=(255, 255, 0))
        
        Display.show_image(img)
        time.sleep_ms(30)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    if touch is not None:
        touch.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
